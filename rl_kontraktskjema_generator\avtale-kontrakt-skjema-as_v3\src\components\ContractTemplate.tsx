import React from 'react';

export interface ContractData {
  employeeName: string;
  employeeAddress: string;
  employeeBirthDate: string;
  startDate: string;
  employmentType: 'permanent' | 'temporary';
  temporaryUntil: string;
  temporaryReason: string;
  hasProbation: boolean;
  probationMonths: string;
  jobTitle: string;
  accountNumber: string;
}

interface ContractTemplateProps {
  data: ContractData;
  className?: string;
}

const ContractTemplate: React.FC<ContractTemplateProps> = ({ data, className = '' }) => {
  const formatDate = (dateString: string) => {
    if (!dateString) return '__.__.__';
    const date = new Date(dateString);
    return date.toLocaleDateString('no-NO');
  };

  const formatField = (value: string, fallbackLength: number = 20) => {
    return value || '_'.repeat(fallbackLength);
  };

  return (
    <div className={`contract-document ${className}`}>
      <h1 className="contract-title">ARBEIDSAVTALE</h1>
      
      <div className="parties-section">
        <div className="party-block">
          <strong>Arbeidsgiver</strong><br/>
          Ringerike Landskap AS<br/>
          Org.nr 924 826 541<br/>
          Birchs vei 7, 3530 Røyse
        </div>
        
        <div className="party-block">
          <strong>Arbeidstaker</strong><br/>
          Navn: {formatField(data.employeeName, 28)}<br/>
          Adresse: {formatField(data.employeeAddress, 25)}<br/>
          Fødselsdato: {formatField(data.employeeBirthDate, 21)}
        </div>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>1 Ansettelsesforhold</h2>
        <ul className="contract-list">
          <li><strong>Startdato:</strong> {formatDate(data.startDate) || '_'.repeat(19)}</li>
          <li>
            <strong>Ansettelsestype:</strong> {data.employmentType === 'permanent' ? '☑ Fast ☐ Midlertidig' : '☐ Fast ☑ Midlertidig'}
            {data.employmentType === 'temporary' ? ` t.o.m. ${formatDate(data.temporaryUntil) || '_'.repeat(12)}` : ''}
          </li>
          {data.employmentType === 'temporary' && (
            <li><em>Grunnlag (hvis midlertidig): {formatField(data.temporaryReason, 33)}</em></li>
          )}
          <li>
            <strong>Prøvetid:</strong> {data.hasProbation ? `☑ ${data.probationMonths || '___'} mnd` : '☑ Ingen'} (maks 6)
          </li>
          {data.hasProbation && (
            <li><em>Gjensidig oppsigelsesfrist i prøvetid: 14 dager</em></li>
          )}
        </ul>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>2 Arbeidssted</h2>
        <p>Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt. Hvis variabelt arbeidssted, gjelder dette som hovedregel.</p>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>3 Stilling & oppgaver</h2>
        <p>
          <strong>Stilling:</strong> {formatField(data.jobTitle, 31)}<br/>
          Arbeid innen anleggsgartner‑ og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten.
        </p>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>4 Arbeidstid & pauser</h2>
        <ul className="contract-list">
          <li>Ordinær tid: <strong>37,5 t per uke</strong>, normalt kl. 07:00 – 15:00.</li>
          <li>Minst én 30 min pause ved arbeidsdag over 5,5 t.</li>
          <li>Overtid honoreres med <strong>≥ 40 %</strong> tillegg etter AML § 10‑6.</li>
        </ul>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>5 Lønn & godtgjørelse</h2>
        <table className="compensation-table">
          <tbody>
            <tr>
              <td><strong>Element</strong></td>
              <td><strong>Sats / tidspunkt</strong></td>
            </tr>
            <tr>
              <td>Timesats</td>
              <td><strong>kr 300,-</strong></td>
            </tr>
            <tr>
              <td>Utbetaling</td>
              <td>5. hver måned til konto nr.: {formatField(data.accountNumber, 10)}</td>
            </tr>
            <tr>
              <td>Overtidstillegg</td>
              <td>≥ 40 % av timelønn</td>
            </tr>
            <tr>
              <td>Kjøring egen bil</td>
              <td>Statens trekkfrie sats – pt. <strong>3,50 kr/km</strong></td>
            </tr>
            <tr>
              <td>Pensjon</td>
              <td>OTP ‑ minimum 2 % av lønn</td>
            </tr>
          </tbody>
        </table>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>6 Ferie & feriepenger</h2>
        <ul className="contract-list">
          <li><strong>5 uker ferie</strong> pr. år (Ferieloven).</li>
          <li>Feriepenger <strong>12 %</strong>; utbetales før hovedferie / ved fratreden når aktuelt.</li>
        </ul>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>7 Oppsigelse</h2>
        <table className="termination-table">
          <tbody>
            <tr>
              <td><strong>Situasjon</strong></td>
              <td><strong>Frist</strong></td>
            </tr>
            <tr>
              <td>I prøvetid</td>
              <td>14 dager</td>
            </tr>
            <tr>
              <td>Etter prøvetid</td>
              <td>1 måned gjensidig</td>
            </tr>
          </tbody>
        </table>
        <p className="legal-note">Oppsigelse skal skje skriftlig iht. AML kap. 15.</p>
      </div>

      <hr className="section-divider"/>

      <div className="contract-section">
        <h2>8 Diverse vilkår</h2>
        <ul className="contract-list">
          <li>Arbeidstaker følger instruks, HMS‑rutiner og bruk av verneutstyr.</li>
          <li>Arbeidsgiver stiller nødvendig arbeidstøy og verktøy.</li>
          <li>Ingen tariffavtale er gjeldende pr. dags dato.</li>
          <li>Endringer i arbeidsforholdet dokumenteres skriftlig som vedlegg til denne avtalen.</li>
        </ul>
      </div>

      <hr className="section-divider"/>

      <div className="signature-section">
        <h2>Signaturer</h2>
        <table className="signature-table">
          <tbody>
            <tr>
              <td><strong>Sted / dato:</strong> Røyse, {formatDate(data.startDate) || '_'.repeat(14)}</td>
              <td></td>
            </tr>
            <tr>
              <td><strong>Arbeidsgiver:</strong> _________________________________</td>
              <td><strong>Arbeidstaker:</strong> _________________________________</td>
            </tr>
          </tbody>
        </table>
      </div>

      <hr className="section-divider"/>

      <div className="footer-note">
        <em>Avtalen er inngått i to eksemplarer; hver part beholder ett.</em>
      </div>
    </div>
  );
};

export default ContractTemplate;
