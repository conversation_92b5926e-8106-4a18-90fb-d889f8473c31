/* Contract Document Styles - Shared between web preview and print */

.contract-document {
  font-family: 'Times New Roman', serif;
  line-height: 1.4;
  color: #000;
  max-width: 210mm;
  margin: 0 auto;
  padding: 20mm;
  background: white;
  font-size: 11pt;
}

.contract-title {
  text-align: center;
  font-size: 16pt;
  font-weight: bold;
  margin-bottom: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.parties-section {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.party-block {
  flex: 1;
  margin-right: 20px;
}

.party-block:last-child {
  margin-right: 0;
}

.section-divider {
  border: none;
  border-top: 1px solid #000;
  margin: 12px 0;
}

.contract-section {
  margin-bottom: 12px;
}

.contract-section h2 {
  font-size: 12pt;
  font-weight: bold;
  margin-bottom: 6px;
  color: #000;
}

.contract-list {
  margin: 0;
  padding-left: 20px;
  list-style-type: disc;
}

.contract-list li {
  margin-bottom: 2px;
}

.compensation-table, 
.termination-table, 
.signature-table {
  width: 100%;
  border-collapse: collapse;
  margin: 8px 0;
}

.compensation-table td, 
.termination-table td {
  border: 1px solid #000;
  padding: 4px 8px;
  font-size: 10pt;
}

.signature-table td {
  padding: 8px;
  vertical-align: bottom;
}

.legal-note {
  font-size: 10pt;
  margin-top: 4px;
}

.signature-section {
  margin-top: 20px;
}

.footer-note {
  text-align: center;
  font-size: 9pt;
  margin-top: 15px;
}

/* Print-specific styles */
@media print {
  body * {
    visibility: hidden;
  }
  
  .contract-document, 
  .contract-document * {
    visibility: visible;
  }
  
  .contract-document {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    padding: 15mm;
    font-size: 10pt;
    page-break-inside: avoid;
  }
  
  .contract-title {
    font-size: 14pt;
  }
  
  .contract-section h2 {
    font-size: 11pt;
  }
  
  .parties-section {
    page-break-inside: avoid;
  }
  
  .contract-section {
    page-break-inside: avoid;
  }
  
  .signature-section {
    page-break-inside: avoid;
  }
  
  /* Hide any UI elements that shouldn't print */
  .no-print {
    display: none !important;
  }
}

/* PDF-specific styles for consistent rendering */
.contract-document.pdf-mode {
  font-size: 10pt;
  padding: 15mm;
  max-width: 190mm;
}

.contract-document.pdf-mode .contract-title {
  font-size: 14pt;
}

.contract-document.pdf-mode .contract-section h2 {
  font-size: 11pt;
}

/* Live preview mode - slightly larger for better readability */
.contract-document.preview-mode {
  font-size: 11pt;
  padding: 20mm;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}
